# Code Pattern Examples

This folder contains key code patterns from the Kanga Sportlink frontend project. These examples serve as templates and reference implementations for AI-assisted development.

## 📁 Pattern Files

### Core Architecture
- **`app-setup.example.tsx`** - Main app configuration with providers and routing setup
- **`route-configuration.example.tsx`** - Route definition patterns with role-based access
- **`context-provider.example.tsx`** - Context provider pattern with state management

### Components
- **`reusable-component.example.tsx`** - Standard component structure with TypeScript interfaces
- **`form-component.example.tsx`** - Form handling with validation and submission patterns
- **`data-table.example.tsx`** - Data display and pagination patterns

### Hooks & Data Management
- **`custom-hook.example.tsx`** - Custom hook pattern with context integration
- **`api-query.example.tsx`** - React Query patterns for data fetching
- **`offline-aware-query.example.tsx`** - Offline-first data management patterns

### Authentication & Authorization
- **`auth-context.example.tsx`** - Authentication state management
- **`protected-route.example.tsx`** - Route protection and role-based access

### Testing
- **`e2e-test.example.ts`** - End-to-end testing patterns with Playwright
- **`component-test.example.tsx`** - Component testing patterns

### Configuration
- **`project-config.example.ts`** - Project configuration and environment setup
- **`sdk-integration.example.ts`** - SDK integration patterns

## 🎯 Usage Guidelines

### For AI Assistants
1. **Reference these patterns** when implementing similar functionality
2. **Follow the established conventions** for naming, structure, and organization
3. **Maintain consistency** with the existing codebase architecture
4. **Use TypeScript interfaces** as defined in the examples
5. **Follow the error handling patterns** shown in the examples

### Key Conventions
- **File Structure**: Each component has its own folder with index.ts, component.tsx, and optional .module.css
- **TypeScript**: Strict typing with interfaces for all props and state
- **Error Handling**: Consistent error boundaries and toast notifications
- **State Management**: Context-based state with useReducer patterns
- **API Integration**: React Query with offline-aware capabilities
- **Styling**: Tailwind CSS with custom component classes
- **Testing**: Playwright for E2E, component-level testing patterns

### Architecture Principles
1. **Separation of Concerns**: Clear separation between UI, business logic, and data
2. **Reusability**: Components designed for reuse across different contexts
3. **Type Safety**: Comprehensive TypeScript coverage
4. **Offline-First**: All data operations support offline scenarios
5. **Role-Based Access**: Consistent authorization patterns throughout
6. **Performance**: Lazy loading and optimization patterns

## 🔧 Implementation Notes

### Context Usage
- Use `useContexts()` hook for accessing global state and API methods
- Follow the established pattern for context providers in App.tsx
- Maintain consistent error handling across all contexts

### Component Patterns
- Export components through index.ts files
- Use TypeScript interfaces for all props
- Implement proper loading and error states
- Follow the established CSS module patterns

### API Integration
- Use React Query for all data fetching
- Implement offline-aware queries where appropriate
- Follow consistent error handling and toast notification patterns
- Use the established SDK patterns for API calls

### Route Management
- Use role-based route protection
- Follow the established wrapper pattern (AdminWrapper, TrainerWrapper, etc.)
- Implement proper loading states for route transitions

## 📋 Checklist for New Features

When implementing new features, ensure:
- [ ] TypeScript interfaces are defined
- [ ] Error boundaries are implemented
- [ ] Loading states are handled
- [ ] Offline scenarios are considered
- [ ] Role-based access is implemented
- [ ] Toast notifications are used consistently
- [ ] Components follow the established folder structure
- [ ] Tests are written following the established patterns

## 🚀 Getting Started

1. Review the relevant example files for your use case
2. Copy the pattern and adapt it to your specific needs
3. Ensure all TypeScript interfaces are properly defined
4. Test both online and offline scenarios
5. Verify role-based access controls work correctly
6. Add appropriate error handling and user feedback

These examples represent the core patterns used throughout the Kanga Sportlink application and should be used as the foundation for all new development work.
