import React, { useState, useEffect, useMemo } from "react";
import { useContexts } from "@/hooks/useContexts";
import { Athlete, AthleteFilters, AthleteTable } from "@/components/Athletes";
import { useGetPaginateQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { User } from "@/interfaces/model.interface";
import { useCustomModelQuery } from "@/query/shared";
import { ToastStatusEnum } from "@/utils/Enums";

const ListAdminAthletePage: React.FC = () => {
  const { globalDispatch, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("Today");
  const [statusFilter, setStatusFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Match the design showing 4 items

  // Build query options for API call
  const queryOptions = useMemo(() => {
    const options: any = {
      size: pageSize,
      page: currentPage,
      filter: ["role_id,eq,member"],
      join: ["enrollment|athlete_id"], // Filter for members only
    };

    // Add search filter if search term exists
    if (searchTerm.trim()) {
      options.filter.push(`first_name,cs,${searchTerm.trim()}`);
    }

    // Add status filter if not "All"
    if (statusFilter !== "All") {
      const statusValue = statusFilter === "Active" ? "active" : "inactive";
      options.filter.push(`status,eq,${statusValue}`);
    }

    // Add date filter logic here if needed
    // For now, we'll skip date filtering

    return options;
  }, [currentPage, pageSize, searchTerm, statusFilter]);

  // Fetch members data using pagination
  const {
    data: membersData,
    isLoading,
    error,
    refetch,
  } = useGetPaginateQuery(Models.MEMBER, queryOptions, {
    enabled: true,
  });

  // Transform User data to Athlete format
  const athletes: Athlete[] = useMemo(() => {
    if (!membersData?.data) return [];

    return membersData.data.map((user: User) => ({
      id: user.id as number,
      dateJoined: user.created_at
        ? new Date(user.created_at).toISOString().split("T")[0]
        : "Unknown",
      status: user.status as Athlete["status"],
      name: JSON.parse(user?.data || "")?.full_name,
      enrollments: user.enrollment, // This would need to be calculated from enrollment data
    }));
  }, [membersData?.data]);

  // Pagination info from API response
  const totalPages = membersData?.num_pages || 1;

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "athletes",
      },
    });
  }, [globalDispatch]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, dateFilter]);

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentPage(1);
    // Refetch data with new filters
    refetch();
  };

  const handleViewList = (_athleteId: number) => {
    // TODO: Navigate to enrollment list page
    // navigate(`/admin/athletes/${athleteId}/enrollments`);
  };

  const handleEditAthlete = (_athleteId: number) => {
    // TODO: Navigate to edit athlete page
    // navigate(`/admin/athletes/${athleteId}/edit`);
  };

  const handleStatusChange = async (
    athleteId: number,
    newStatus: Athlete["status"]
  ) => {
    try {
      // Map the status to the correct format for the API
      const statusValue = newStatus === 1 ? "active" : "inactive";

      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/admin/users/${athleteId}/status`,
        method: "POST",
        body: {
          status: statusValue,
          reason: `Status changed to ${statusValue} by admin`,
          notifyUser: true,
          adminNotes: `Status updated via admin panel`,
        },
      });

      showToast(
        `Athlete status updated to ${statusValue} successfully`,
        5000,
        ToastStatusEnum.SUCCESS
      );

      // Refetch the data to update the table
      refetch();
    } catch (error: any) {
      const message =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to update athlete status";
      showToast(message, 5000, ToastStatusEnum.ERROR);
    }
  };

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Athlete Management</h1>
        </div>

        {/* Filters Section */}
        <AthleteFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          onApplyFilter={handleApplyFilter}
        />

        {/* Athletes Table Section */}
        {isLoading ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-text">Loading athletes...</p>
          </div>
        ) : error ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-red-600">
              Error loading athletes. Please try again.
            </p>
          </div>
        ) : (
          <AthleteTable
            athletes={athletes}
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onViewList={handleViewList}
            onEditAthlete={handleEditAthlete}
            onStatusChange={handleStatusChange}
          />
        )}
      </div>
    </div>
  );
};
export default ListAdminAthletePage;
