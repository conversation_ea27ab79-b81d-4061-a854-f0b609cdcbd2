import React, { useState } from "react";
import { InteractiveButton } from "@/components/InteractiveButton";
import { AddExerciseModal, AddVideoModal } from "./index";
import { Week, Exercise } from "../types";
import { updateExerciseOrdersAndLabels } from "../utils/exerciseUtils";
import { useLibrary } from "@/hooks";

// Use Exercise from types as ExerciseInstance
type ExerciseInstance = Exercise;

interface ExerciseCardProps {
  exercise: ExerciseInstance;
  exerciseIndex: number;
  sessionIndex: number;
  dayIndex: number;
  weekIndex: number;
  onUpdateWeeks: (weeks: Week[]) => void;
  weeks: Week[];
}

const ExerciseCard: React.FC<ExerciseCardProps> = ({
  exercise,
  exerciseIndex,
  sessionIndex,
  dayIndex,
  weekIndex,
  onUpdateWeeks,
  weeks,
}) => {
  const [showAddExerciseModal, setShowAddExerciseModal] = useState(false);
  const [showAddVideoModal, setShowAddVideoModal] = useState(false);

  // Use the library hooks for exercises and videos with larger page size for dropdowns
  const {
    adminLibraryData: adminExercises,
    trainerLibraryData: trainerExercises,
    isLoading: loadingExercises,
    createLibraryItem: createExercise,
    refreshAll: refreshExercises,
  } = useLibrary({
    libraryType: "exercise",
    initialPagination: { limit: 100000 }, // Larger size for dropdown
  });

  const {
    adminLibraryData: adminVideos,
    trainerLibraryData: trainerVideos,
    isLoading: loadingVideos,
    createLibraryItem: createVideo,
    refreshAll: refreshVideos,
  } = useLibrary({
    libraryType: "video",
    initialPagination: { limit: 100000 }, // Larger size for dropdown
  });

  // Combine admin and trainer data for display with proper typing and labeling
  const availableExercises = [...adminExercises, ...trainerExercises].map(
    (ex: any) => ({
      ...ex,
      createdBy: ex.type === 1 ? "admin" : "trainer",
    })
  );

  const availableVideos = [...adminVideos, ...trainerVideos].map(
    (video: any) => ({
      ...video,
      createdBy: video.type === 1 ? "admin" : "trainer",
    })
  );

  // Handle adding new exercise
  const handleAddExercise = async (
    exerciseName: string,
    saveToDatabase: boolean
  ) => {
    try {
      if (saveToDatabase) {
        await createExercise({ name: exerciseName, type: 2 });
        refreshExercises();
      }
      // Note: If not saving to database, the modal will handle adding it locally
      setShowAddExerciseModal(false);
    } catch (error) {
      console.error("Failed to create exercise:", error);
    }
  };

  // Handle adding new video
  const handleAddVideo = async (
    videoName: string,
    videoUrl: string,
    saveToDatabase: boolean
  ) => {
    try {
      if (saveToDatabase) {
        await createVideo({ name: videoName, url: videoUrl, type: 2 });
        refreshVideos();
      }
      // Note: If not saving to database, the modal will handle adding it locally
      setShowAddVideoModal(false);
    } catch (error) {
      console.error("Failed to create video:", error);
    }
  };

  // Generate exercise label based on linking
  const generateExerciseLabel = () => {
    // Now that labels are stored in the data, we can simply use them
    if (exercise.is_linked && exercise.label) {
      // Linked exercise: show label + number (e.g., "A1", "A2")
      return `${exercise.label}${exercise.label_number}`;
    } else if (exercise.label) {
      // Unlinked exercise: show just the label (e.g., "A", "B")
      return exercise.label;
    } else {
      // Fallback for exercises that haven't been processed yet
      return String.fromCharCode(64 + exercise.exercise_order);
    }
  };

  // Check if this exercise is linked and not the last in its group
  // If so, rest duration should be disabled (automatically set to 0)
  const isLinkedAndNotLast = () => {
    if (!exercise.is_linked || !exercise.label) return false;

    // Get all exercises in the same session
    const session = weeks[weekIndex].days[dayIndex].sessions[sessionIndex];
    const exercises = session.exercises;

    // Find all exercises in the same linked group
    const linkedGroup = exercises.filter(
      (ex) => ex.is_linked && ex.label === exercise.label
    );

    if (linkedGroup.length <= 1) return false;

    // Sort by exercise_order to find the last exercise
    linkedGroup.sort((a, b) => a.exercise_order - b.exercise_order);
    const lastExercise = linkedGroup[linkedGroup.length - 1];

    // Return true if this exercise is not the last in the group
    return exercise.id !== lastExercise.id;
  };

  const restDurationDisabled = isLinkedAndNotLast();

  const deleteExercise = () => {
    const updatedWeeks = [...weeks];
    const exercises =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex].exercises;

    // Remove the exercise
    exercises.splice(exerciseIndex, 1);

    // Update exercise orders and reassign labels for the entire session
    updateExerciseOrdersAndLabels(exercises);

    onUpdateWeeks(updatedWeeks);
  };

  const updateExercise = (field: string, value: any) => {
    const updatedWeeks = [...weeks];
    const exerciseRef =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex].exercises[
        exerciseIndex
      ];

    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      (exerciseRef as any)[parent][child] = value;
    } else {
      (exerciseRef as any)[field] = value;
    }

    // If updating fields that might affect exercise structure, update labels
    // Note: Most field updates don't affect labeling, but we could add specific checks here if needed
    // For now, we'll only update labels for structural changes (handled in other functions)

    onUpdateWeeks(updatedWeeks);
  };

  return (
    <>
      <div className="bg-background border border-border rounded-md shadow-sm p-4 relative w-full">
        {/* Exercise Label */}
        <div className="absolute -left-12 top-8 w-9 h-9 bg-primary rounded flex items-center justify-center">
          <span className="text-white text-base font-bold font-['Inter']">
            {generateExerciseLabel()}
          </span>
        </div>

        {/* Delete Button */}
        <div className="absolute top-2 right-2">
          <button
            type="button"
            className="w-3.5 h-4 rounded"
            onClick={deleteExercise}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="16"
              viewBox="0 0 14 16"
              fill="none"
            >
              <g clipPath="url(#clip0_89_2339)">
                <path
                  d="M4.225 0.553125L4 1H1C0.446875 1 0 1.44687 0 2C0 2.55312 0.446875 3 1 3H13C13.5531 3 14 2.55312 14 2C14 1.44687 13.5531 1 13 1H10L9.775 0.553125C9.60625 0.2125 9.25938 0 8.88125 0H5.11875C4.74062 0 4.39375 0.2125 4.225 0.553125ZM13 4H1L1.6625 14.5938C1.7125 15.3844 2.36875 16 3.15937 16H10.8406C11.6312 16 12.2875 15.3844 12.3375 14.5938L13 4Z"
                  fill="#EF4444"
                />
              </g>
              <defs>
                <clipPath id="clip0_89_2339">
                  <path d="M0 0H14V16H0V0Z" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* Exercise Selection */}
          <div className="space-y-2">
            <label className="text-sm font-normal text-text font-['Inter']">
              Choose Exercise
            </label>
            <div className="flex gap-2">
              <select
                className="flex-1 h-10 px-2 bg-input border border-border rounded text-text"
                value={exercise.exercise_id}
                onChange={(e) => updateExercise("exercise_id", e.target.value)}
                disabled={loadingExercises}
              >
                <option value="">
                  {loadingExercises
                    ? "Loading exercises..."
                    : "Choose exercise..."}
                </option>
                {availableExercises.map((ex) => (
                  <option key={ex.id} value={ex.id}>
                    {ex.name}{" "}
                    {ex.createdBy === "admin" ? "(Admin)" : "(My Exercise)"}
                  </option>
                ))}
              </select>
              <InteractiveButton
                type="button"
                onClick={() => setShowAddExerciseModal(true)}
                className="flex w-[1.875rem] h-[2.5rem] py-[0.625rem] px-[0.5rem] bg-primary rounded items-center justify-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="16"
                  viewBox="0 0 14 16"
                  fill="none"
                >
                  <path d="M14 16H0V0H14V16Z" stroke="none" />
                  <path
                    d="M8 2.5C8 1.94687 7.55312 1.5 7 1.5C6.44688 1.5 6 1.94687 6 2.5V7H1.5C0.946875 7 0.5 7.44688 0.5 8C0.5 8.55312 0.946875 9 1.5 9H6V13.5C6 14.0531 6.44688 14.5 7 14.5C7.55312 14.5 8 14.0531 8 13.5V9H12.5C13.0531 9 13.5 8.55312 13.5 8C13.5 7.44688 13.0531 7 12.5 7H8V2.5Z"
                    fill="white"
                  />
                </svg>
              </InteractiveButton>
            </div>
          </div>

          {/* Sets and Reps/Time */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Sets */}
            <div className="space-y-2">
              <label className="text-sm font-normal text-text font-['Inter']">
                Sets
              </label>
              <input
                type="text"
                className="w-full h-10 px-3 bg-input border border-border rounded text-text"
                placeholder="Enter sets..."
                value={exercise.sets}
                onChange={(e) => updateExercise("sets", e.target.value)}
              />
            </div>

            {/* Reps/Time */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-normal text-text font-['Inter']">
                  Reps/Time
                </label>
                <div className="flex bg-input border border-border rounded overflow-hidden">
                  <button
                    type="button"
                    className={`px-3 py-1 text-xs font-normal font-['Inter'] ${
                      exercise.reps_time_type === "reps"
                        ? "bg-primary text-white"
                        : "bg-transparent text-text-secondary"
                    }`}
                    onClick={() => updateExercise("reps_time_type", "reps")}
                  >
                    Reps
                  </button>
                  <button
                    type="button"
                    className={`px-3 py-1 text-xs font-normal font-['Inter'] ${
                      exercise.reps_time_type === "time"
                        ? "bg-primary text-white"
                        : "bg-transparent text-text-secondary"
                    }`}
                    onClick={() => updateExercise("reps_time_type", "time")}
                  >
                    Time
                  </button>
                </div>
              </div>
              <input
                type="text"
                className="w-full h-10 px-3 bg-input border border-border rounded text-text"
                placeholder={
                  exercise.reps_time_type === "reps"
                    ? "Enter reps..."
                    : "Enter time..."
                }
                value={exercise.reps_or_time}
                onChange={(e) => updateExercise("reps_or_time", e.target.value)}
              />
            </div>
          </div>

          {/* Add Video */}
          <div className="space-y-2">
            <label className="text-sm font-normal text-text font-['Inter']">
              Add Video
            </label>
            <div className="flex gap-2">
              <select
                className="flex-1 h-10 px-3 bg-input border border-border rounded text-text"
                value={exercise.video_id || ""}
                onChange={(e) => updateExercise("video_id", e.target.value)}
                disabled={loadingVideos}
              >
                <option value="">
                  {loadingVideos ? "Loading videos..." : "Select video..."}
                </option>
                {availableVideos.map((video) => (
                  <option key={video.id} value={video.id}>
                    {video.name}{" "}
                    {video.createdBy === "admin" ? "(Admin)" : "(My Video)"}
                  </option>
                ))}
              </select>
              <InteractiveButton
                type="button"
                onClick={() => setShowAddVideoModal(true)}
                className="flex w-[1.875rem] h-[2.5rem] py-[0.625rem] px-[0.5rem] bg-primary rounded  items-center justify-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="16"
                  viewBox="0 0 14 16"
                  fill="none"
                >
                  <path d="M14 16H0V0H14V16Z" stroke="none" />
                  <path
                    d="M8 2.5C8 1.94687 7.55312 1.5 7 1.5C6.44688 1.5 6 1.94687 6 2.5V7H1.5C0.946875 7 0.5 7.44688 0.5 8C0.5 8.55312 0.946875 9 1.5 9H6V13.5C6 14.0531 6.44688 14.5 7 14.5C7.55312 14.5 8 14.0531 8 13.5V9H12.5C13.0531 9 13.5 8.55312 13.5 8C13.5 7.44688 13.0531 7 12.5 7H8V2.5Z"
                    fill="white"
                  />
                </svg>
              </InteractiveButton>
            </div>
          </div>

          {/* Exercise Details and Rest Duration */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Exercise Details */}
            <div className="space-y-2">
              <label className="text-sm font-normal text-text font-['Inter']">
                Exercise Details
              </label>
              <textarea
                className="w-full h-24 px-3 py-2 bg-input border border-border rounded text-text resize-none"
                placeholder="Enter exercise details..."
                value={exercise.exercise_details}
                onChange={(e) =>
                  updateExercise("exercise_details", e.target.value)
                }
              />
            </div>

            {/* Rest Duration */}
            <div className="space-y-2">
              <label className="text-sm font-normal text-text font-['Inter']">
                Rest Duration
                {restDurationDisabled && (
                  <span className="text-xs text-text-secondary ml-2">
                    (Disabled - linked exercise)
                  </span>
                )}
              </label>
              <div className="flex items-center gap-2 mt-2">
                <div className="space-y-1">
                  <input
                    type="number"
                    min="0"
                    max="59"
                    className={`w-16 h-10 px-2 border rounded text-center ${
                      restDurationDisabled
                        ? "bg-background-secondary border-border text-gray-500 cursor-not-allowed"
                        : "bg-input border-border text-text"
                    }`}
                    placeholder="00"
                    value={exercise.rest_duration_minutes || 0}
                    disabled={restDurationDisabled}
                    onChange={(e) =>
                      updateExercise(
                        "rest_duration_minutes",
                        parseInt(e.target.value) || 0
                      )
                    }
                  />
                  <div className="text-xs text-text-secondary text-center">
                    MM
                  </div>
                </div>
                <span className="text-lg text-text">:</span>
                <div className="space-y-1">
                  <input
                    type="number"
                    min="0"
                    max="59"
                    className={`w-16 h-10 px-2 border rounded text-center ${
                      restDurationDisabled
                        ? "bg-background-secondary border-border text-gray-500 cursor-not-allowed"
                        : "bg-input border-border text-text"
                    }`}
                    placeholder="00"
                    value={exercise.rest_duration_seconds || 0}
                    disabled={restDurationDisabled}
                    onChange={(e) =>
                      updateExercise(
                        "rest_duration_seconds",
                        parseInt(e.target.value) || 0
                      )
                    }
                  />
                  <div className="text-xs text-text-secondary text-center">
                    SS
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Exercise Modal */}
      <AddExerciseModal
        isOpen={showAddExerciseModal}
        onClose={() => setShowAddExerciseModal(false)}
        onAddExercise={handleAddExercise}
      />

      {/* Add Video Modal */}
      <AddVideoModal
        isOpen={showAddVideoModal}
        onClose={() => setShowAddVideoModal(false)}
        onAddVideo={handleAddVideo}
      />
    </>
  );
};

export default ExerciseCard;
