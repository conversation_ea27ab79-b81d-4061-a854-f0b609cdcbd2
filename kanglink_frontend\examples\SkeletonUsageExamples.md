import React from 'react';
import { 
  DiscountPageSkeleton, 
  FormPageSkeleton, 
  ListPageSkeleton 
} from '@/components/Skeleton';

/**
 * Examples of how to use different skeleton components
 */

// Example 1: Using DiscountPageSkeleton (already implemented)
export const DiscountPageExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return <DiscountPageSkeleton />;
  }
  
  return <div>Your discount page content here</div>;
};

// Example 2: Using FormPageSkeleton for create/edit pages
export const CreateProgramPageExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return (
      <FormPageSkeleton
        showHeader={true}
        columns={2}
        sections={6}
        fieldsPerSection={4}
        showActions={true}
      />
    );
  }
  
  return <div>Your create program form here</div>;
};

// Example 3: Using FormPageSkeleton for single column forms
export const ProfilePageExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return (
      <FormPageSkeleton
        showHeader={true}
        columns={1}
        sections={4}
        fieldsPerSection={3}
        showActions={true}
        className="max-w-2xl mx-auto"
      />
    );
  }
  
  return <div>Your profile form here</div>;
};

// Example 4: Using ListPageSkeleton for admin list pages
export const AdminProgramsListExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return (
      <ListPageSkeleton
        showHeader={true}
        showFilters={true}
        showStats={true}
        statsCount={4}
        rows={10}
        columns={7}
        showPagination={true}
      />
    );
  }
  
  return <div>Your programs list here</div>;
};

// Example 5: Using ListPageSkeleton for simple lists without stats
export const TrainerListExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return (
      <ListPageSkeleton
        showHeader={true}
        showFilters={true}
        showStats={false}
        rows={8}
        columns={5}
        showPagination={true}
      />
    );
  }
  
  return <div>Your trainers list here</div>;
};

// Example 6: Using ListPageSkeleton for dashboard with stats only
export const DashboardExample: React.FC = () => {
  const isLoading = true; // Replace with actual loading state
  
  if (isLoading) {
    return (
      <ListPageSkeleton
        showHeader={true}
        showFilters={false}
        showStats={true}
        statsCount={6}
        rows={5}
        columns={4}
        showPagination={false}
      />
    );
  }
  
  return <div>Your dashboard content here</div>;
};

/**
 * How to integrate with existing pages:
 * 
 * 1. Import the appropriate skeleton component:
 *    import { FormPageSkeleton } from '@/components/Skeleton';
 * 
 * 2. Replace your loading state:
 *    // Before:
 *    if (isLoading) {
 *      return <div>Loading...</div>;
 *    }
 * 
 *    // After:
 *    if (isLoading) {
 *      return <FormPageSkeleton columns={2} sections={4} />;
 *    }
 * 
 * 3. Customize the skeleton to match your page structure:
 *    - Use `columns={1}` for single column forms
 *    - Use `columns={2}` for two column layouts
 *    - Adjust `sections` and `fieldsPerSection` to match your form
 *    - Set `showStats={true}` for pages with stat cards
 *    - Set `showFilters={true}` for pages with search/filter functionality
 */

export default {
  DiscountPageExample,
  CreateProgramPageExample,
  ProfilePageExample,
  AdminProgramsListExample,
  TrainerListExample,
  DashboardExample,
};
