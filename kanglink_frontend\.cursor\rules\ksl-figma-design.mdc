---
description: 
globs: 
alwaysApply: false
---
we wil take each UI design from figma selection and ensure they are implemented on the tagged page
the format is 

```
[@page]
[design - figma selection link]
OR 
[design:
light - figma selection link
dark - figma selection link]
```

we have to do it one step at a time


[LoginPage.tsx](mdc:src/pages/Common/Auth/LoginPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=76-7&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=112-2133&m=dev

[AthleteSignup.tsx](mdc:src/pages/Athlete/Auth/AthleteSignup.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=76-524&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=113-2933&m=dev

[TrainerSignup.tsx](mdc:src/pages/Trainer/Auth/TrainerSignup.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=115-3061&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=113-2700&m=dev

[ChangePassword.tsx](mdc:src/pages/Trainer/Auth/ChangePassword.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=115-3292&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=113-2618&m=dev

[ForgotPassword.tsx](mdc:src/pages/Trainer/Auth/ForgotPassword.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=115-3330&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=113-2657&m=dev

[ViewTrainerDashboardPage.tsx](mdc:src/pages/Trainer/View/ViewTrainerDashboardPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=76-941&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=76-1165&m=dev

[ListTrainerProgramPage.tsx](mdc:src/pages/Trainer/List/ListTrainerProgramPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=89-1429&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=112-1770&m=dev

[AddTrainerProgramPage.tsx](mdc:src/pages/Trainer/Add/AddTrainerProgramPage.tsx) and [CreateProgramStepOne.tsx](mdc:src/components/CreateProgramStepOne/CreateProgramStepOne.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=89-1793&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-3382&m=dev

[AddTrainerProgramPage.tsx](mdc:src/pages/Trainer/Add/AddTrainerProgramPage.tsx) and [CreateProgramStepTwo.tsx](mdc:src/components/CreateProgramStepTwo/CreateProgramStepTwo.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=89-2022&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-4943&m=dev

[AddTrainerProgramPage.tsx](mdc:src/pages/Trainer/Add/AddTrainerProgramPage.tsx) and [CreateProgramPreview.tsx](mdc:src/components/CreateProgramPreview/CreateProgramPreview.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-11428&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-3606&m=dev

[ViewTrainerDiscountPage.tsx](mdc:src/pages/Trainer/View/ViewTrainerDiscountPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=92-30&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-3970&m=dev

[ListTrainerAthleteManagementPage.tsx](mdc:src/pages/Trainer/List/ListTrainerAthleteManagementPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=92-309&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-4203&m=dev

[ViewTrainerFeedPage.tsx](mdc:src/pages/Trainer/View/ViewTrainerFeedPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=94-95&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-4440&m=dev

[ViewTrainerTransactionsPage.tsx](mdc:src/pages/Trainer/View/ViewTrainerTransactionsPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=94-330&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-4633&m=dev

[ViewTrainerProfilePage.tsx](mdc:src/pages/Trainer/View/ViewTrainerProfilePage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-163&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=116-4762&m=dev

[HomePage.tsx](mdc:src/pages/Athlete/View/HomePage.tsx), [Trainers.tsx](mdc:src/playground/components/Trainers/Trainers.tsx), [Programs.tsx](mdc:src/components/Programs/Programs.tsx), [TrainerCard.tsx](mdc:src/playground/components/TrainerCard/TrainerCard.tsx), [TrainersYML.tsx](mdc:src/components/TrainersYML/TrainersYML.tsx), [TopTrainers.tsx](mdc:src/components/TopTrainers/TopTrainers.tsx), [ProgramCard.tsx](mdc:src/components/ProgramCard/ProgramCard.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-474&m=dev and https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-1054&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-5479&m=dev and https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-6059&m=dev

[ViewAthleteTrainerDetailsPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteTrainerDetailsPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-1872&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-6877&m=dev

[ViewAthleteProgramPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteProgramPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-2128&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-7137&m=dev

[ViewAthleteLibraryPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteLibraryPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-2474&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-7485&m=dev

[ViewAthleteProgramPreviewPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteProgramPreviewPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-3036&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-7635&m=dev

[ViewAthleteOwnedProgramPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteOwnedProgramPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-3332&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-8329&m=dev

[ViewAthleteFeedPage.tsx](mdc:src/pages/Athlete/View/ViewAthleteFeedPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-3695&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-8692&m=dev

[ViewAthleteProfilePage.tsx](mdc:src/pages/Athlete/View/ViewAthleteProfilePage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-3855&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=118-8844&m=dev

[AdminLoginPage2.tsx](mdc:src/pages/Admin/Auth/AdminLoginPage2.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-3982&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-9077&m=dev

[ViewAdminDashboardPage.tsx](mdc:src/pages/Admin/View/ViewAdminDashboardPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-4044&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-9136&m=dev

[ListAdminProgramPage.tsx](mdc:src/pages/Admin/List/ListAdminProgramPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-11114&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-10258&m=dev

[ListAdminAthletePage.tsx](mdc:src/pages/Admin/List/ListAdminAthletePage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=95-4196&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-9293&m=dev

[ListAdminTrainerPage.tsx](mdc:src/pages/Admin/List/ListAdminTrainerPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=99-1277&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-11792&m=dev

[ListAdminTransactionPage.tsx](mdc:src/pages/Admin/List/ListAdminTransactionPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=99-11&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-9587&m=dev

[ListAdminRefundPage.tsx](mdc:src/pages/Admin/List/ListAdminRefundPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=99-355&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-9931&m=dev

[ListAdminLibraryPage.tsx](mdc:src/pages/Admin/List/ListAdminLibraryPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=99-679&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-10572&m=dev

[ViewAdminProfilePage.tsx](mdc:src/pages/Admin/View/ViewAdminProfilePage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=99-1121&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=120-11009&m=dev






