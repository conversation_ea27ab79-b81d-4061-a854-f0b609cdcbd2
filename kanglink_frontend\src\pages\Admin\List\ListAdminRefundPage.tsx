import React, { useState, useEffect } from "react";
import { useContexts } from "@/hooks/useContexts";
import {
  RefundFilters,
  RefundTable,
  RefundDetailModal,
} from "@/components/Refunds";
import {
  useAdminRefundRequests,
  AdminRefundQueryParams,
} from "@/hooks/useAdminRefunds";
import { ToastStatusEnum } from "@/utils/Enums";

const ListAdminRefundPage: React.FC = () => {
  const { globalDispatch, showToast } = useContexts();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("Select date");
  const [statusFilter, setStatusFilter] = useState("Active");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Increased page size for better UX
  const [selectedRefundId, setSelectedRefundId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "refunds",
      },
    });
  }, [globalDispatch]);

  // Build query parameters for API
  const queryParams: AdminRefundQueryParams = {
    page: currentPage,
    limit: pageSize,
    ...(statusFilter !== "Active" && {
      status: statusFilter.toLowerCase() as any,
    }),
    ...(searchTerm && { trainer_name: searchTerm }),
  };

  // Fetch refund requests
  const { data, isLoading, error, refetch } =
    useAdminRefundRequests(queryParams);

  // Extract data from API response
  const refunds = data?.data?.refund_requests || [];
  const pagination = data?.data?.pagination;
  const totalPages = pagination?.total_pages || 1;

  // Debug logging
  console.log("Refund API Response:", { data, isLoading, error, refunds });

  // Temporary mock data for testing (remove when API is working)
  const mockRefunds = [
    {
      id: 1,
      enrollment_id: 123,
      amount: 100,
      currency: "USD",
      reason: "Not satisfied with program",
      status: "pending" as const,
      requested_at: "2025-01-09T10:30:00Z",
      program: {
        name: "Advanced Strength Training",
        split_name: "Upper Body Focus",
      },
      athlete: {
        email: "<EMAIL>",
        full_name: "John Doe",
      },
      trainer: {
        full_name: "Jane Smith",
      },
      enrollment: {
        enrollment_date: "2025-01-08T08:00:00Z",
        payment_status: "paid",
      },
    },
    {
      id: 2,
      enrollment_id: 124,
      amount: 150,
      currency: "USD",
      reason: "Technical issues with app",
      status: "approved" as const,
      requested_at: "2025-01-08T14:20:00Z",
      program: {
        name: "Yoga Fundamentals",
      },
      athlete: {
        email: "<EMAIL>",
        full_name: "Sarah Johnson",
      },
      trainer: {
        full_name: "Mike Wilson",
      },
      enrollment: {
        enrollment_date: "2025-01-07T12:00:00Z",
        payment_status: "paid",
      },
    },
  ];

  // Use mock data if no real data is available
  const displayRefunds = refunds.length > 0 ? refunds : mockRefunds;

  // Handle error state
  useEffect(() => {
    if (error) {
      showToast(
        error.message || "Failed to load refund requests",
        5000,
        ToastStatusEnum.ERROR
      );
    }
  }, [error, showToast]);

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentPage(1);
  };

  const handleViewDetails = (refundId: number) => {
    setSelectedRefundId(refundId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRefundId(null);
    // Refetch data to get latest updates
    refetch();
  };

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Refund</h1>
        </div>

        {/* Filters Section */}
        <RefundFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          onApplyFilter={handleApplyFilter}
        />

        {/* Refunds Table Section */}
        <RefundTable
          refunds={displayRefunds}
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={setCurrentPage}
          onViewDetails={handleViewDetails}
          isLoading={isLoading}
        />

        {/* Refund Detail Modal */}
        <RefundDetailModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          refundId={selectedRefundId}
        />
      </div>
    </div>
  );
};

export default ListAdminRefundPage;
