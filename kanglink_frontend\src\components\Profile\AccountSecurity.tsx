import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface AccountSecurityProps {
  onChangePassword?: () => void;
  onTwoFactorAuth?: () => void;
  className?: string;
  disabled?: boolean;
}

const AccountSecurity: React.FC<AccountSecurityProps> = ({
  onChangePassword,
  onTwoFactorAuth,
  className = "",
  disabled = false
}) => {
  return (
    <div className={`bg-background-secondary border border-border rounded-lg p-4 sm:p-6 shadow-sm ${className}`}>
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-text">Account Security</h2>

        <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Change Password Button */}
          <InteractiveButton
            onClick={onChangePassword}
            disabled={disabled}
            type="button"
            className="!h-11 w-full bg-transparent border border-primary text-primary font-semibold hover:bg-primary hover:text-white active:bg-primary-active transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-primary-disabled disabled:text-primary-disabled"
          >
            Change Password
          </InteractiveButton>

          {/* Two-Factor Authentication Button */}
          <InteractiveButton
            onClick={onTwoFactorAuth}
            disabled={disabled}
            type="button"
            className="!h-11 w-full bg-transparent border border-primary text-primary font-semibold hover:bg-primary hover:text-white active:bg-primary-active transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-primary-disabled disabled:text-primary-disabled"
          >
            Two-Factor Authentication
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default AccountSecurity;
