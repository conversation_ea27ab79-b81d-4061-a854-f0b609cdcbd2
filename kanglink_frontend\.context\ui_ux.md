# UI/UX Guidelines

## Theme System

### Theme Configuration
- Theme constants defined in `src/context/Theme/ThemeConstants.ts`
- Theme styles implemented in `src/components/ThemeStyles/ThemeStyles.tsx`
- Tailwind configuration in `tailwind.config.ts`

### Usage Guidelines
1. **Use Tailwind Classes First**
   - Use theme-aware Tailwind classes for styling whenever possible
   - Example: `text-primary bg-surface border-border`

2. **Theme Hook Usage**
   - Use `useTheme` hook only when classes cannot be used
   - Import from `src/hooks/useTheme/useTheme.tsx`
   ```tsx
   const { state } = useTheme();
   const isDarkMode = state?.theme === Theme.DARK;
   ```

3. **Light/Dark Mode Support**
   - All components must support both light and dark modes
   - Use CSS custom properties for theme-aware styling
   - Test all components in both themes

## Component Structure

### Component Organization
- Components should be organized in dedicated folders:
  ```
  components/
    ComponentName/
      index.ts             # Export file
      ComponentName.tsx    # Component implementation
      ComponentName.types.ts # TypeScript interfaces (if needed)
  ```

### Component Guidelines
1. **Wrap with LazyLoad**
   - All components must be wrapped with `LazyLoad`
   - Export components from their respective `index.ts` files

2. **Proper TypeScript Typing**
   - Use interfaces from `src/interfaces`
   - Use enums from `src/utils/Enums`
   - Properly type all components, props, and event handlers

3. **Accessibility**
   - Use semantic HTML elements
   - Include proper ARIA attributes when needed
   - Ensure keyboard navigation works
   - Maintain sufficient color contrast
   - Support screen readers

## Loading States

### Skeleton Loading
- Use skeleton components for loading states
- Match skeleton structure to the actual content
- Available skeleton components:
  - `Skeleton` - Basic skeleton loader
  - `DiscountPageSkeleton` - For discount management pages
  - `FormPageSkeleton` - For form-based pages
  - `ListPageSkeleton` - For list/table pages

### Error Handling
- Use `ErrorBoundary` component to catch and display errors
- Provides theme-aware error UI
- Includes "Try Again" functionality for recovery

## Form Handling
- Use react-hook-form with yup and yupResolver
- Integrate with `MkdInputV2` components
- Follow established form validation patterns

## Performance Considerations
1. Memoize expensive calculations with `useMemo`
2. Optimize re-renders with `React.memo` when appropriate
3. Use `useCallback` for event handlers passed to child components
4. Avoid unnecessary state updates