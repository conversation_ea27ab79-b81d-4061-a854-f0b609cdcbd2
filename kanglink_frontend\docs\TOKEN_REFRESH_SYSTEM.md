# Token Refresh System

This document explains the automatic token refresh system implemented in the application. The system provides seamless background token refresh when users have enabled "Remember Me" during login.

## Overview

The token refresh system consists of several components:

1. **TokenManager** - Core singleton class that handles token validation and refresh
2. **useTokenRefresh** - React hook for token management in components
3. **Enhanced SDK methods** - Updated MkdSDK and TreeSDK with automatic token refresh
4. **AuthContext integration** - Automatic initialization and cleanup

## How It Works

### Automatic Background Refresh

When a user logs in with "Remember Me" enabled:

1. The refresh token is stored in localStorage
2. TokenManager automatically schedules token refresh before expiration
3. Tokens are refreshed silently in the background
4. No user interaction is required

### Token Validation

Before making API calls, the system:

1. Checks if the current token is expired
2. Automatically refreshes if needed and possible
3. Proceeds with the API call using the valid token
4. Handles refresh failures gracefully

## Usage

### Basic Usage with Hooks

```typescript
import { useTokenRefresh } from '@/hooks/useTokenRefresh';

function MyComponent() {
  const { ensureValidToken, tokenStatus, refreshToken } = useTokenRefresh();

  const handleApiCall = async () => {
    // Ensure token is valid before making API call
    const isValid = await ensureValidToken();
    if (!isValid) {
      // Handle token refresh failure
      return;
    }

    // Make your API call here
    const response = await sdk.someApiCall();
  };

  return (
    <div>
      <p>Token Status: {tokenStatus.isExpired ? 'Expired' : 'Valid'}</p>
      <p>Expires in: {tokenStatus.expiresIn} seconds</p>
      <button onClick={handleApiCall}>Make API Call</button>
      <button onClick={refreshToken}>Manual Refresh</button>
    </div>
  );
}
```

### Using Enhanced SDK Methods

```typescript
import { useSDK } from '@/hooks/useSDK';

function MyComponent() {
  const { sdk } = useSDK();

  const handleApiCall = async () => {
    try {
      // Use requestWithTokenRefresh for automatic token handling
      const response = await sdk.requestWithTokenRefresh({
        endpoint: '/api/some-endpoint',
        method: 'GET'
      });
      
      console.log('API response:', response);
    } catch (error) {
      if (error.message === 'TOKEN_EXPIRED') {
        // Handle token expiration
        console.log('Token expired and could not be refreshed');
      }
    }
  };

  return <button onClick={handleApiCall}>Make API Call</button>;
}
```

### Wrapping SDK Methods

```typescript
import { useSDKWithTokenRefresh } from '@/hooks/useTokenRefresh';
import { useSDK } from '@/hooks/useSDK';

function MyComponent() {
  const { sdk } = useSDK();
  const { wrapSDKMethod } = useSDKWithTokenRefresh();

  const handleGetProfile = async () => {
    // Wrap any SDK method with automatic token refresh
    const getProfileWithRefresh = wrapSDKMethod(sdk.getProfile.bind(sdk));
    
    try {
      const profile = await getProfileWithRefresh();
      console.log('Profile:', profile);
    } catch (error) {
      console.error('Failed to get profile:', error);
    }
  };

  return <button onClick={handleGetProfile}>Get Profile</button>;
}
```

## Configuration

### Login with Remember Me

```typescript
const handleLogin = async (email: string, password: string, rememberMe: boolean) => {
  try {
    const response = await sdk.login(email, password, 'user', rememberMe);
    
    if (!response.error) {
      // Dispatch login action with remember_me flag
      dispatch({
        type: 'LOGIN',
        payload: {
          ...response.data,
          remember_me: rememberMe
        }
      });
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### Manual Token Manager Control

```typescript
import { tokenManager } from '@/utils/TokenManager';

// Check token status
const status = tokenManager.getTokenStatus();
console.log('Token status:', status);

// Manual refresh
const result = await tokenManager.silentTokenRefresh();
if (result.success) {
  console.log('Token refreshed successfully');
} else {
  console.error('Token refresh failed:', result.error);
}

// Ensure valid token
const isValid = await tokenManager.ensureValidToken();
if (!isValid) {
  // Redirect to login or handle as needed
}
```

## Token Status Component

A ready-to-use component for displaying token status:

```typescript
import TokenStatus from '@/components/TokenStatus/TokenStatus';

function AdminPanel() {
  return (
    <div>
      <h1>Admin Panel</h1>
      {/* Show token status for debugging */}
      <TokenStatus />
    </div>
  );
}
```

## Security Considerations

1. **Refresh tokens are stored in localStorage** - Consider using httpOnly cookies for production
2. **Token refresh happens automatically** - Users won't be prompted for re-authentication
3. **Refresh failures are handled gracefully** - Users will be redirected to login when refresh fails
4. **Remember Me is required** - Token refresh only works when user explicitly enables it

## Error Handling

The system handles various error scenarios:

- **Expired refresh token** - User is redirected to login
- **Network failures** - Retries are not implemented, user may need to refresh manually
- **Invalid tokens** - System assumes token is expired and attempts refresh
- **Missing refresh token** - No automatic refresh, user must login again

## Best Practices

1. **Always use ensureValidToken()** before critical API calls
2. **Handle TOKEN_EXPIRED errors** appropriately in your components
3. **Don't rely solely on automatic refresh** - implement proper error handling
4. **Monitor token status** in development using the TokenStatus component
5. **Test with expired tokens** to ensure proper fallback behavior

## Troubleshooting

### Token not refreshing automatically
- Check if "Remember Me" was enabled during login
- Verify refresh_token exists in localStorage
- Check browser console for refresh errors

### API calls failing with expired tokens
- Ensure you're using `requestWithTokenRefresh` or `ensureValidToken`
- Check if refresh token is still valid
- Verify network connectivity

### Memory leaks or multiple refresh attempts
- TokenManager is a singleton, don't create multiple instances
- Cleanup is handled automatically on logout
- Refresh operations are deduplicated to prevent multiple simultaneous requests
