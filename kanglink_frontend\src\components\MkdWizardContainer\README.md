# MkdWizardContainer

A flexible wizard/multi-step form container component that supports both internal navigation buttons and external navigation control via refs.

## Features

- ✅ **Internal Navigation**: Built-in Previous/Next buttons
- ✅ **External Navigation**: Control navigation from parent components using refs
- ✅ **Step Tracking**: Track current step and step changes
- ✅ **Validation Support**: Check if navigation is possible
- ✅ **Flexible UI**: Show/hide internal buttons as needed
- ✅ **TypeScript Support**: Full type safety with TypeScript

## Basic Usage

### With Internal Navigation (Default)

```tsx
import { MkdWizardContainer } from "@/components/MkdWizardContainer";

const MyWizard = () => {
  return (
    <MkdWizardContainer>
      <StepOne componentId={1} />
      <StepTwo componentId={2} />
      <StepThree componentId={3} />
    </MkdWizardContainer>
  );
};
```

### With External Navigation

```tsx
import { useRef } from "react";
import { MkdWizardContainer, MkdWizardContainerRef } from "@/components/MkdWizardContainer";
import { InteractiveButton } from "@/components/InteractiveButton";

const MyWizard = () => {
  const wizardRef = useRef<MkdWizardContainerRef>(null);
  const [currentStep, setCurrentStep] = useState({ activeId: 1, activeIndex: 0 });

  const handleStepChange = (activeId: number, activeIndex: number) => {
    setCurrentStep({ activeId, activeIndex });
  };

  const handlePrevious = () => {
    wizardRef.current?.goToPrevious();
  };

  const handleNext = () => {
    wizardRef.current?.goToNext();
  };

  return (
    <div>
      <MkdWizardContainer 
        ref={wizardRef}
        showButton={false}
        onStepChange={handleStepChange}
      >
        <StepOne componentId={1} />
        <StepTwo componentId={2} />
        <StepThree componentId={3} />
      </MkdWizardContainer>

      {/* External Navigation */}
      <div className="flex justify-between">
        <InteractiveButton
          onClick={handlePrevious}
          disabled={!wizardRef.current?.canGoPrevious()}
        >
          Previous
        </InteractiveButton>
        
        <span>Step {currentStep.activeIndex + 1} of 3</span>
        
        <InteractiveButton
          onClick={handleNext}
          disabled={!wizardRef.current?.canGoNext()}
        >
          Next
        </InteractiveButton>
      </div>
    </div>
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | - | Child components representing wizard steps |
| `className` | `string` | `""` | Additional CSS classes |
| `showButton` | `boolean` | `true` | Show/hide internal navigation buttons |
| `onStepChange` | `(activeId: number, activeIndex: number) => void` | - | Callback fired when step changes |

## Ref Methods

When using `forwardRef`, the following methods are available:

| Method | Return Type | Description |
|--------|-------------|-------------|
| `goToNext()` | `boolean` | Navigate to next step. Returns `true` if successful |
| `goToPrevious()` | `boolean` | Navigate to previous step. Returns `true` if successful |
| `goToStep(stepId)` | `boolean` | Navigate to specific step by ID. Returns `true` if successful |
| `getCurrentStep()` | `{ activeId: number, activeIndex: number }` | Get current step information |
| `canGoNext()` | `boolean` | Check if next navigation is possible |
| `canGoPrevious()` | `boolean` | Check if previous navigation is possible |

## Step Components

Each step component must have a `componentId` prop:

```tsx
interface StepProps {
  componentId: number;
  // ... other props
}

const StepOne: React.FC<StepProps> = ({ componentId, ...props }) => {
  return (
    <div>
      {/* Step content */}
    </div>
  );
};
```

## Advanced Example

```tsx
import { useRef, useState } from "react";
import { MkdWizardContainer, MkdWizardContainerRef } from "@/components/MkdWizardContainer";

const AdvancedWizard = () => {
  const wizardRef = useRef<MkdWizardContainerRef>(null);
  const [currentStep, setCurrentStep] = useState({ activeId: 1, activeIndex: 0 });
  const [stepData, setStepData] = useState({});

  const handleStepChange = (activeId: number, activeIndex: number) => {
    setCurrentStep({ activeId, activeIndex });
  };

  const handleStepSubmit = (stepId: number, data: any) => {
    setStepData(prev => ({ ...prev, [stepId]: data }));
    
    // Auto-advance to next step
    if (wizardRef.current?.canGoNext()) {
      wizardRef.current.goToNext();
    }
  };

  const jumpToStep = (stepId: number) => {
    wizardRef.current?.goToStep(stepId);
  };

  return (
    <div className="space-y-6">
      {/* Step Indicator */}
      <div className="flex justify-center space-x-4">
        {[1, 2, 3].map(stepId => (
          <button
            key={stepId}
            onClick={() => jumpToStep(stepId)}
            className={`w-8 h-8 rounded-full ${
              currentStep.activeId === stepId 
                ? 'bg-primary text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}
          >
            {stepId}
          </button>
        ))}
      </div>

      <MkdWizardContainer 
        ref={wizardRef}
        showButton={false}
        onStepChange={handleStepChange}
      >
        <PersonalInfo 
          componentId={1} 
          onSubmit={(data) => handleStepSubmit(1, data)}
        />
        <ContactInfo 
          componentId={2} 
          onSubmit={(data) => handleStepSubmit(2, data)}
        />
        <Review 
          componentId={3} 
          data={stepData}
          onSubmit={(data) => handleStepSubmit(3, data)}
        />
      </MkdWizardContainer>
    </div>
  );
};
```

## Styling

The component uses Tailwind CSS classes and supports theme tokens:

- `bg-primary` - Primary button background
- `text-white` - Button text color
- `hover:bg-primary-hover` - Hover state
- `disabled:bg-primary-disabled` - Disabled state

## Notes

- Each step component must have a unique `componentId`
- The wizard automatically manages which step is currently active
- Navigation methods return `boolean` to indicate success/failure
- Use `onStepChange` callback to track step changes in parent component
- External navigation gives you full control over the UI and validation logic
