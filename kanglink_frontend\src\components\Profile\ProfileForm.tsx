import React from "react";
import MkdInputV2 from "@/components/MkdInputV2";

interface ProfileFormProps {
  fullName?: string;
  email?: string;
  onFullNameChange?: (value: string) => void;
  onEmailChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  fullName = "",
  email = "",
  onFullNameChange,
  onEmailChange,
  className = "",
  disabled = false
}) => {
  return (
    <div className={`bg-background-secondary border border-border rounded-lg p-4 sm:p-6 shadow-sm ${className}`}>
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-text">Profile Management</h2>

        <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Full Name Field */}
          <div className="space-y-2">
            <MkdInputV2
              name="fullName"
              type="text"
              value={fullName}
              onChange={(e) => onFullNameChange?.(e.target.value)}
              disabled={disabled}
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className="text-sm font-medium text-text">
                  Full Name
                </MkdInputV2.Label>
                <MkdInputV2.Field
                  placeholder="Full Name"
                  className="w-full h-11 px-3 py-2 bg-background border border-border rounded-md text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors duration-200 hover:border-border-hover disabled:bg-background-disabled disabled:cursor-not-allowed"
                />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <MkdInputV2
              name="email"
              type="email"
              value={email}
              onChange={(e) => onEmailChange?.(e.target.value)}
              disabled={disabled}
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className="text-sm font-medium text-text">
                  Email
                </MkdInputV2.Label>
                <MkdInputV2.Field
                  placeholder="<EMAIL>"
                  className="w-full h-11 px-3 py-2 bg-background border border-border rounded-md text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors duration-200 hover:border-border-hover disabled:bg-background-disabled disabled:cursor-not-allowed"
                />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileForm;
