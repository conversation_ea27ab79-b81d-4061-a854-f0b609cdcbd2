import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface FormActionButtonsProps {
  onSaveAsDraft?: () => void;
  onPublish?: () => void;
  onBack?: () => void;
  onPreview?: () => void;
  isSubmitting?: boolean;
  isSaving?: boolean;
  isPublishing?: boolean;
  showBackButton?: boolean;
  program?: any;
}

const FormActionButtons: React.FC<FormActionButtonsProps> = ({
  onSaveAsDraft,
  onPublish,
  onBack,
  onPreview,
  isSubmitting = false,
  isSaving = false,
  isPublishing = false,
  showBackButton = false,
  program,
}) => {
  const showNext = ["pending_approval", "published"].includes(program?.status);

  return (
    <div className="flex justify-between gap-4 pt-6">
      {/* Left side - Back button */}
      <div className="flex gap-4">
        {showBackButton && (
          <InteractiveButton
            type="button"
            onClick={onBack}
            className="bg-transparent text-primary border-primary hover:bg-primary-hover hover:text-white"
            style={{
              width: "7.49613rem",
              height: "2.625rem",
              padding: "0.75rem 1.5rem",
            }}
            disabled={isSubmitting || isSaving || isPublishing}
          >
            Back
          </InteractiveButton>
        )}
      </div>

      {/* Right side - Save as Draft and Publish buttons */}
      <div className="flex gap-4">
        <InteractiveButton
          type="button"
          onClick={onSaveAsDraft}
          className="bg-transparent text-primary border-primary hover:bg-primary-hover hover:text-white"
          style={{
            width: "7.49613rem",
            height: "2.625rem",
            padding: "0.75rem 1.5rem",
          }}
          disabled={isSubmitting || isSaving || isPublishing}
          loading={isSaving}
        >
          {isSaving
            ? "Saving..."
            : program?.programId
              ? "Update"
              : "Save as Draft"}
        </InteractiveButton>
        <InteractiveButton
          type="button"
          onClick={() => {
            if (showNext) {
              onPreview?.();
            } else {
              onPublish?.();
            }
          }}
          className={`bg-primary text-white border-primary hover:bg-primary-hover`}
          style={{
            width: "7.49613rem",
            height: "2.625rem",
            padding: "0.75rem 1.5rem",
          }}
          disabled={isSubmitting || isSaving || isPublishing}
          loading={isPublishing}
        >
          {isPublishing ? "Publishing..." : showNext ? "Next" : "Publish"}
        </InteractiveButton>
      </div>
    </div>
  );
};

export default FormActionButtons;
