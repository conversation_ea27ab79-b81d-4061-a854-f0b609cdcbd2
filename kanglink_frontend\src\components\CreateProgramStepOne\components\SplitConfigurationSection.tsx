import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { FormData } from "../types";

interface SplitConfigurationSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
  splitProgramValue: number | undefined;
  onIncrementSplitProgram: () => void;
  onDecrementSplitProgram: () => void;
}

const SplitConfigurationSection: React.FC<SplitConfigurationSectionProps> = ({
  register,
  errors,
  splitProgramValue,
  onIncrementSplitProgram,
  onDecrementSplitProgram,
}) => {
  const currencyOptions = [
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
    { value: "CAD", label: "CAD" },
  ];

  return (
    <div className="space-y-6">
      {/* Split Program */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-text">Split Program</label>
        <div className="flex items-center border border-border rounded-md bg-input">
          <button
            type="button"
            disabled={splitProgramValue === 1}
            onClick={onDecrementSplitProgram}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-l-md"
          >
            −
          </button>
          <MkdInputV2
            type="number"
            name="split_program"
            register={register}
            errors={errors}
            className="w-16 text-center border-0 bg-transparent text-text focus:ring-0"
            disabled
          >
            <MkdInputV2.Container>
              <MkdInputV2.Field
                className="border-0 shadow-none text-center bg-transparent text-text focus:ring-0"
                min={1}
                max={3}
              />
            </MkdInputV2.Container>
          </MkdInputV2>
          <button
            type="button"
            disabled={splitProgramValue === 3}
            onClick={onIncrementSplitProgram}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-r-md"
          >
            +
          </button>
        </div>
      </div>

      {/* Currency Selection */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-text">Currency</label>
        <MkdInputV2
          name="currency"
          type="select"
          register={register}
          errors={errors}
          options={currencyOptions}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Field className="w-20 bg-none" />
          </MkdInputV2.Container>
        </MkdInputV2>
      </div>
    </div>
  );
};

export default SplitConfigurationSection;
