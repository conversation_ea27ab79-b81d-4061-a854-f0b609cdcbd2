# Project Structure

```
project/
├── .cursor/                    # Cursor editor configuration
│   └── rules/                  # Project-specific rules for development
├── docs/                       # Project documentation
│   ├── TOKEN_REFRESH_SYSTEM.md # Authentication token refresh documentation
│   ├── SKELETON_LOADING_SYSTEM.md # Loading state system documentation
│   └── ATHLETE_ENDPOINTS_IMPLEMENTATION.md # API implementation guide
├── src/
│   ├── components/             # Reusable UI components
│   │   ├── ErrorBoundary/      # Error handling component
│   │   ├── SplitCard/          # Card component for program splits
│   │   ├── ThemeStyles/        # Theme-related styling components
│   │   └── MkdInputV2/         # Form input components
│   ├── context/                # React context providers
│   │   ├── Global/             # Global application state
│   │   ├── Offline/            # Offline mode functionality
│   │   └── Theme/              # Theme management
│   ├── hooks/                  # Custom React hooks
│   │   ├── useOffline/         # Hook for offline functionality
│   │   ├── useSDK/             # Hook for SDK access
│   │   ├── useTheme/           # Hook for theme management
│   │   ├── useTokenRefresh/    # Hook for token refresh
│   │   └── useAthleteEnrollments/ # Hook for athlete data
│   ├── interfaces/             # TypeScript interfaces
│   │   └── model.interface.ts  # Data model interfaces
│   ├── pages/                  # Application pages
│   │   ├── Athlete/            # Athlete-related pages
│   │   │   └── View/           # Athlete view pages
│   │   └── Trainer/            # Trainer-related pages
│   │       └── List/           # Trainer list pages
│   ├── routes/                 # Routing configuration
│   │   ├── LazyLoad.ts         # Component lazy loading
│   │   └── Routes.tsx          # Application routes
│   ├── test/                   # Test files
│   │   ├── e2e/                # End-to-end tests with Playwright
│   │   ├── unit/               # Unit tests
│   │   └── integration/        # Integration tests
│   └── utils/                  # Utility functions
│       ├── baas/               # Backend-as-a-Service utilities
│       │   ├── athlete.ts      # Athlete API configuration
│       │   └── admin-dashboard/ # Admin dashboard utilities
│       ├── Enums/              # Enumeration types
│       └── offline/            # Offline mode utilities
└── tailwind.config.ts         # Tailwind CSS configuration