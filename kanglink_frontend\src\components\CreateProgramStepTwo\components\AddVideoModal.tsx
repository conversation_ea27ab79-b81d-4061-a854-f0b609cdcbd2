import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface AddVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddVideo: (
    videoName: string,
    videoUrl: string,
    saveToDatabase: boolean
  ) => void;
}

const AddVideoModal: React.FC<AddVideoModalProps> = ({
  isOpen,
  onClose,
  onAddVideo,
}) => {
  const [videoName, setVideoName] = useState("");
  const [videoUrl, setVideoUrl] = useState("");
  const [saveToMyVideos, setSaveToMyVideos] = useState(false);

  const handleSubmit = () => {
    if (videoName.trim() && videoUrl.trim()) {
      onAddVideo(videoName.trim(), videoUrl.trim(), saveToMyVideos);
      // Reset form
      setVideoName("");
      setVideoUrl("");
      setSaveToMyVideos(false);
      onClose();
    }
  };

  const handleClose = () => {
    // Reset form on close
    setVideoName("");
    setVideoUrl("");
    setSaveToMyVideos(false);
    onClose();
  };

  const isValid = videoName.trim() && videoUrl.trim();

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      title="Upload"
      modalHeader={true}
      classes={{
        modalDialog: "!w-full md:!w-[25rem] !h-fit",
        modalContent: "!px-5 !pt-5",
        modal: "h-full",
      }}
    >
      <div className="space-y-6">
        {/* Video Name Input */}
        <MkdInputV2
          name="videoName"
          type="text"
          value={videoName}
          onChange={(e) => setVideoName(e.target.value)}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video name" />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Add Video Link Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
              <svg
                className="w-4 h-4 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
            </div>
            <span className="text-sm font-medium text-text">
              Add Video Link
            </span>
          </div>

          <MkdInputV2
            name="videoUrl"
            type="url"
            value={videoUrl}
            onChange={(e) => setVideoUrl(e.target.value)}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Field placeholder="Enter video URL" />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>

        {/* Save to My Videos Checkbox */}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="saveToMyVideos"
            checked={saveToMyVideos}
            onChange={(e) => setSaveToMyVideos(e.target.checked)}
            className="text-primary"
          />
          <label
            htmlFor="saveToMyVideos"
            className="text-sm text-text cursor-pointer"
          >
            Save to My Videos
          </label>
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-border rounded text-text hover:bg-input"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={handleSubmit}
            disabled={!isValid}
            className={`px-4 py-2 rounded text-white ${
              isValid
                ? "bg-primary hover:bg-primary-hover"
                : "bg-gray-400 cursor-not-allowed"
            }`}
          >
            Save
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default AddVideoModal;
