# Equipment Implementation for Program Splits

## Overview
This document describes the implementation of split-specific equipment functionality in the CreateProgramStepTwo component.

## Problem
Previously, the equipment input was not connected to any split-specific data and was not included in the save payload. Each split requires equipment to be entered and saved as part of the split data.

## Solution
Implemented a complete equipment management system that:

1. **Tracks equipment per split** - Each split can have its own equipment requirements
2. **Persists equipment data** - Equipment is saved and loaded with program data
3. **Updates dynamically** - Equipment input changes based on selected split
4. **Includes in payload** - Equipment is properly included in API calls

## Implementation Details

### 1. State Management
Added `splitEquipment` state to track equipment for each split:

```typescript
const [splitEquipment, setSplitEquipment] = useState<{
  [key: string]: string;
}>(() => {
  // Initialize from existing data or stepOneData
  if (initialData?.splitEquipment) {
    return initialData.splitEquipment;
  }
  if (stepOneData?.splits) {
    const equipmentMap: { [key: string]: string } = {};
    stepOneData.splits.forEach((split: any) => {
      if (split.equipment_required) {
        equipmentMap[split.split_id] = split.equipment_required;
      }
    });
    return equipmentMap;
  }
  return {};
});
```

### 2. Equipment Change Handler
Added handler to update equipment when user types:

```typescript
const handleEquipmentChange = (splitId: string, equipment: string) => {
  setSplitEquipment(prev => ({
    ...prev,
    [splitId]: equipment
  }));
};
```

### 3. Updated ProgramSetupSection Component
Enhanced the component to:
- Accept `splitEquipment` and `onEquipmentChange` props
- Display split-specific equipment input
- Show current split name in label
- Handle equipment changes

```typescript
<MkdInputV2
  name={`equipment_${currentSplit}`}
  type="textarea"
  value={splitEquipment?.[currentSplit || ""] || ""}
  onChange={handleEquipmentChange}
>
  <MkdInputV2.Container>
    <MkdInputV2.Label className="text-base font-normal text-text">
      Equipment Required for {stepOneData?.splits?.find(
        (s: any) => s.split_id === currentSplit
      )?.title || "Current Split"}
    </MkdInputV2.Label>
    <MkdInputV2.Field
      placeholder="List required equipment for this split..."
      rows="3"
      className="mt-2"
    />
    <MkdInputV2.Error />
  </MkdInputV2.Container>
</MkdInputV2>
```

### 4. Payload Enhancement
Modified payload construction to include equipment in stepOneData splits:

```typescript
const enhancedStepOneData = {
  ...stepOneData,
  splits: stepOneData?.splits?.map((split: any) => ({
    ...split,
    equipment_required: splitEquipment[split.split_id] || ""
  })) || []
};

const payload = {
  stepOneData: enhancedStepOneData,
  image: imageUrl,
  stepTwoData: data,
  status: "draft", // or "pending_approval"
};
```

### 5. Data Persistence
Equipment data is included in both:
- `validateAndPrepareData()` function via `splitEquipment` field
- Enhanced stepOneData in API payload

## Usage

### For Users
1. Select a split from the dropdown
2. Enter equipment requirements in the textarea
3. Switch between splits to enter different equipment for each
4. Equipment is automatically saved when the program is saved/published

### For Developers
The equipment system is fully integrated and requires no additional setup. The component automatically:
- Loads existing equipment data when editing programs
- Tracks changes per split
- Includes equipment in API calls
- Handles split switching seamlessly

## Data Flow

1. **Load**: Equipment loaded from `stepOneData.splits[].equipment_required` or `initialData.splitEquipment`
2. **Edit**: User changes equipment via textarea input
3. **Store**: Equipment stored in `splitEquipment` state by split ID
4. **Save**: Equipment included in API payload via enhanced stepOneData

## API Structure

The equipment data is sent to the backend as part of the splits in stepOneData:

```json
{
  "stepOneData": {
    "splits": [
      {
        "split_id": "split-1",
        "title": "Upper Body",
        "equipment_required": "Dumbbells, Barbell, Bench",
        "full_price": 99.99,
        "subscription": 19.99
      }
    ]
  },
  "stepTwoData": {
    "splitEquipment": {
      "split-1": "Dumbbells, Barbell, Bench"
    }
  }
}
```

## Testing

A test file `equipment-test.ts` is included to verify the functionality works correctly. The test validates:
- Equipment loading from stepOneData
- Equipment state management
- Payload construction with equipment data

## Files Modified

1. `CreateProgramStepTwo.tsx` - Main component logic
2. `ProgramSetupSection.tsx` - Equipment input UI
3. `equipment-test.ts` - Test validation (new)
4. `EQUIPMENT_IMPLEMENTATION.md` - This documentation (new)
