import {
  Week as WeekModel,
  Day as DayModel,
  Session as SessionModel,
  ExerciseInstance as ExerciseInstanceModel,
  Program,
} from "@/interfaces";
import { StepOneFormData } from "../CreateProgramStepOne";

export interface Exercise extends ExerciseInstanceModel {
  // Override required fields from model
  id: string;
  sets: string;
  reps_or_time: string;
  reps_time_type: "reps" | "time";
  exercise_details: string;
  rest_duration_minutes: number;
  rest_duration_seconds: number;
  label: string | null;
  label_number: string;
  is_linked: boolean;
  exercise_order: number;
}

export interface Session extends SessionModel {
  // Override required fields from model
  id: string;
  title: string;
  exercises: Exercise[];
}

export interface Day extends DayModel {
  // Override required fields from model
  id: string;
  title: string;
  is_rest_day: boolean;
  sessions: Session[];
}

export interface Week extends WeekModel {
  // Override required fields from model
  id: string;
  title: string;
  days: Day[];
}

export interface ProgramFormData extends Program {
  weeks: Week[];
}

export interface CreateProgramStepTwoProps {
  onSubmit?: (data: any) => void;
  onCancel?: () => void;
  onBack?: () => void;
  onPreview?: () => void;
  componentId?: number;
  stepOneData?: StepOneFormData | null;
  initialData?: any;
}
