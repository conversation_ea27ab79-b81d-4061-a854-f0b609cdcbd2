import React, { useEffect, Suspense } from "react";
import { useContexts } from "@/hooks/useContexts";
import {
  AdminDashboardStatsGrid,
  AdminDashboardQuickLinks,
  AdminDashboardAlerts,
} from "@/components/AdminDashboard";
import { useAdminDashboard } from "@/hooks/useAdminDashboard";

const ViewAdminDashboardPage: React.FC = () => {
  const { globalDispatch } = useContexts();

  // Fetch dashboard data
  const { stats, alerts, isStatsLoading, isAlertsLoading, error } =
    useAdminDashboard({
      enabled: true,
      refreshInterval: 5 * 60 * 1000, // Refresh every 5 minutes
    });

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "dashboard",
      },
    });
  }, [globalDispatch]);

  // Show error state if there's an error
  if (error) {
    return (
      <div className="relative flex flex-col gap-6 px-4 py-6 w-full max-w-[1200px] mx-auto min-h-screen bg-background text-text">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-2">
              Error Loading Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full min-h-screen bg-background p-4 sm:p-6 transition-colors duration-200">
      {/* Page Title */}
      <h1 className="text-2xl font-bold text-text mb-6 sm:mb-8">Dashboard</h1>

      {/* Stats Cards */}
      <Suspense
        fallback={
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-32"
              ></div>
            ))}
          </div>
        }
      >
        <AdminDashboardStatsGrid stats={stats} isLoading={isStatsLoading} />
      </Suspense>

      <div className="flex flex-col lg:flex-row justify-between gap-6">
        {/* Quick Links */}
        <Suspense
          fallback={
            <div className="w-full lg:w-[740px] animate-pulse">
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-6 w-32 mb-4"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-gray-200 dark:bg-gray-700 rounded h-20"
                  ></div>
                ))}
              </div>
            </div>
          }
        >
          <AdminDashboardQuickLinks />
        </Suspense>

        {/* Alerts */}
        <Suspense
          fallback={
            <div className="w-full lg:w-[370px] animate-pulse">
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-6 w-16 mb-4"></div>
              <div className="bg-gray-200 dark:bg-gray-700 rounded h-32"></div>
            </div>
          }
        >
          <AdminDashboardAlerts alerts={alerts} isLoading={isAlertsLoading} />
        </Suspense>
      </div>
    </div>
  );
};

export default ViewAdminDashboardPage;
