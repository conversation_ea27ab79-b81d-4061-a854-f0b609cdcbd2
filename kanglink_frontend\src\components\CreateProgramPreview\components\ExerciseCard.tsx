import React from "react";
import { ChevronUpIcon } from "@/assets/svgs";

interface Exercise {
  id: string;
  name: string;
  sets: string;
  reps_or_time: string;
  reps_time_type: "reps" | "time";
  video_url: string;
  rest_duration_minutes: number;
  rest_duration_seconds: number;
  linked_exercise_id?: string | null;
  is_linked?: boolean;
  label?: string | null;
  label_number?: string;
  exercise_order?: number;
}

interface ExerciseCardProps {
  exercise: Exercise;
  exerciseLabel: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const ExerciseCard: React.FC<ExerciseCardProps> = ({
  exercise,
  exerciseLabel,
  isCollapsed = false,
  onToggleCollapse,
}) => {
  const formatRestDuration = () => {
    const minutes = exercise.rest_duration_minutes || 0;
    const seconds = exercise.rest_duration_seconds || 0;
    if (minutes > 0 && seconds > 0) {
      return `${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else if (seconds > 0) {
      return `${seconds}s`;
    }
    return "0s";
  };

  const formatRepsOrTime = () => {
    if (!exercise.reps_or_time) return "N/A";
    return exercise.reps_time_type === "time"
      ? `${exercise.reps_or_time}s`
      : exercise.reps_or_time;
  };

  const formatVideoLink = () => {
    if (!exercise.video_url) return "No video";
    // Truncate long URLs for display
    if (exercise.video_url.length > 20) {
      return exercise.video_url.substring(0, 20) + "...";
    }
    return exercise.video_url;
  };

  return (
    <div className="bg-background border border-border rounded-md shadow-sm overflow-hidden">
      {/* Exercise Header */}
      <div className="p-4 flex items-center gap-4">
        {/* Exercise Thumbnail */}
        <div className="w-16 h-12 bg-background-secondary rounded-md overflow-hidden relative">
          <img
            src="https://placehold.co/64x48"
            alt="Exercise thumbnail"
            className="w-full h-full object-cover"
          />
          {/* Play button overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-6 h-6 bg-black/50 rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-sm"></div>
            </div>
          </div>
        </div>

        {/* Exercise Status Indicator */}
        <div className="w-6 h-6 flex items-center justify-center">
          <div className="w-4 h-4 bg-green-400 rounded-full"></div>
        </div>

        {/* Exercise Name */}
        <div className="flex-1">
          <h4 className="text-base font-medium text-text">
            {exercise.name === "Choose Exercise"
              ? `Exercise ${exerciseLabel}`
              : exercise.name}
          </h4>
        </div>

        {/* Collapse Toggle */}
        <button
          onClick={onToggleCollapse}
          className="w-6 h-6 flex items-center justify-center text-text-secondary hover:text-text"
        >
          <ChevronUpIcon
            className={`w-4 h-4 transition-transform ${
              isCollapsed ? "rotate-180" : ""
            }`}
            stroke="currentColor"
          />
        </button>
      </div>

      {/* Exercise Details */}
      {!isCollapsed && (
        <div className="px-4 pb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-0 sm:ml-20">
            {/* Left Column */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                <span className="text-base text-text">Sets</span>
                <span className="text-base text-text font-medium">
                  {exercise.sets || "N/A"}
                </span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                <span className="text-base text-text">Video Link</span>
                <span className="text-base text-primary font-medium truncate">
                  {formatVideoLink()}
                </span>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                <span className="text-base text-text">
                  {exercise.reps_time_type === "time" ? "Time" : "Repetitions"}
                </span>
                <span className="text-base text-text font-medium">
                  {formatRepsOrTime()}
                </span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                <span className="text-base text-text">Rest</span>
                <span className="text-base text-text font-medium">
                  {formatRestDuration()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExerciseCard;
