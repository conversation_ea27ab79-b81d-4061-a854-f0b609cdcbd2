# Bug Tracking

## Known Issues

### Data Fetching
- **Stale Data Issue**: View pages (like ViewAthleteProgramPage) were showing stale cached data when users navigate away and return
  - **Status**: Fixed by setting staleTime to 0 in React Query hooks
  - **Location**: `src/pages/Athlete/View/`
  - **Reference**: See `src/pages/Athlete/View/docs/FRESH_DATA_FIX.md`

### Authentication
- **Token Refresh**: Potential race condition in token refresh system when multiple requests expire simultaneously
  - **Status**: In progress
  - **Location**: Token refresh system in `src/hooks/useTokenRefresh`
  - **Reference**: See `docs/TOKEN_REFRESH_SYSTEM.md`

### Offline Mode
- **Storage Quota**: Potential issue with storage quota being exceeded in offline mode
  - **Status**: Needs investigation
  - **Location**: `src/context/Offline/OfflineReducer.ts`
  - **Reference**: See `src/utils/offline/README.md` troubleshooting section

### UI Components
- **ErrorBoundary Process.env Error**: Fixed issue with `process.env.NODE_ENV` causing TypeScript errors
  - **Status**: Fixed
  - **Location**: `src/components/ErrorBoundary`
  - **Reference**: See `src/components/ErrorBoundary/README.md`

## TODOs and FIXMEs

### Testing
- **TODO**: Implement unit tests for core components
  - **Location**: `src/test/README.md`
  - **Priority**: Medium

### API Integration
- **TODO**: Complete athlete progress tracking system integration
  - **Location**: `src/pages/Athlete/View/docs/athlete_progress_tracking.md`
  - **Priority**: High

### Performance
- **TODO**: Optimize skeleton loading performance for complex layouts
  - **Location**: `docs/SKELETON_LOADING_SYSTEM.md`
  - **Priority**: Low

### Accessibility
- **TODO**: Ensure all components meet accessibility standards
  - **Location**: Multiple components
  - **Priority**: High