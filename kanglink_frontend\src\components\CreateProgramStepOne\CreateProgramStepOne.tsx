import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  ProgramBasicInfoSection,
  PaymentPlansSection,
  ProgramSettingsSection,
  TargetLevelsSection,
  SplitConfigurationSection,
  SplitDetailsSection,
  PreviewSection,
  FormActionButtons,
} from "./components";
import { CreateProgramStepOneProps } from "./types";
import { generateUUID } from "@/utils";
import { InteractiveButton } from "@/components/InteractiveButton";
import { programStepOneData } from "@/assets/data";

// Form validation schema
const schema = yup.object({
  program_name: yup.string().required("Program name is required"),
  type_of_program: yup.string().required("Type of program is required"),
  program_description: yup
    .string()
    .required("Program description is required")
    .max(500, "Program description cannot exceed 500 characters")
    .trim(),
  payment_plan: yup
    .array()
    .of(yup.string())
    .min(1, "At least one payment plan must be selected")
    .required("Payment plan is required"),
  track_progress: yup.boolean(),
  allow_comments: yup.boolean(),
  allow_private_messages: yup.boolean(),
  target_levels: yup
    .array()
    .of(yup.string())
    .min(1, "At least one target level must be selected")
    .required("Target levels are required"),
  split_program: yup
    .number()
    .min(1, "Split program must be at least 1")
    .required("Split program is required"),
  splits: yup
    .array()
    .of(
      yup.object({
        split_id: yup.string().required(),
        title: yup.string().required("Split title is required"),
        full_price: yup.number().min(0, "Price must be positive").optional(),
        subscription: yup
          .number()
          .min(0, "Subscription price must be positive")
          .optional(),
      })
    )
    .required("Split details are required"),
  currency: yup.string().required("Currency is required"),
  days_for_preview: yup
    .number()
    .min(1, "Days for preview must be at least 1")
    .max(7, "Preview days cannot exceed 7 days")
    .required("Days for preview is required"),
});

type FormData = yup.InferType<typeof schema>;

const CreateProgramStepOne: React.FC<CreateProgramStepOneProps> = ({
  onSubmit,
  onCancel,
  initialData,
}) => {
  // Create default values and merge with initial data if provided
  const getDefaultValues = (): FormData => {
    const defaults: FormData = {
      program_name: "",
      type_of_program: "Body building",
      program_description: "",
      payment_plan: [],
      track_progress: false,
      allow_comments: false,
      allow_private_messages: false,
      target_levels: [],
      split_program: 1,
      splits: [
        {
          title: "",
          full_price: 0,
          subscription: 0,
          split_id: generateUUID(),
        },
      ],
      currency: "USD",
      days_for_preview: 1,
    };

    // Merge with initial data if provided
    if (initialData) {
      return {
        ...defaults,
        ...initialData,
        // Ensure splits array is properly merged
        splits:
          initialData.splits && initialData.splits.length > 0
            ? initialData.splits
            : defaults.splits,
      };
    }

    return defaults;
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    mode: "onChange",
    defaultValues: getDefaultValues(),
  });

  const splitProgramValue = watch("split_program");
  const daysForPreviewValue = watch("days_for_preview");
  const splitsValue = watch("splits");
  const programDescriptionValue = watch("program_description");
  const paymentPlanValue = watch("payment_plan");

  // Effect to ensure splits array matches splitProgram count when component mounts with initial data
  useEffect(() => {
    if (initialData && splitProgramValue) {
      const currentSplits = splitsValue || [];
      const targetCount = splitProgramValue;

      if (currentSplits.length !== targetCount) {
        const newSplits = [...currentSplits];

        // Add missing splits
        while (newSplits.length < targetCount) {
          newSplits.push({
            title: "",
            full_price: 0,
            subscription: 0,
            split_id: generateUUID(),
          });
        }

        // Remove extra splits
        if (newSplits.length > targetCount) {
          newSplits.splice(targetCount);
        }

        setValue("splits", newSplits);
      }
    }
  }, [initialData, splitProgramValue, splitsValue, setValue]);

  // Debug logging to verify data persistence (can be removed in production)
  useEffect(() => {
    if (initialData) {
      console.log(
        "CreateProgramStepOne: Restoring data from previous step:",
        initialData
      );
    }
  }, [initialData]);

  const handleFormSubmit = (data: FormData) => {
    // Custom validation for price fields based on payment plan
    const paymentPlan = data.payment_plan || [];
    const hasOneTime = paymentPlan.includes("oneTime");
    const hasMonthly = paymentPlan.includes("monthly");

    // Validate that required price fields are filled
    for (const split of data.splits) {
      if (hasOneTime && (!split.full_price || split.full_price <= 0)) {
        // You could set custom errors here if needed
        return;
      }
      if (hasMonthly && (!split.subscription || split.subscription <= 0)) {
        // You could set custom errors here if needed
        return;
      }
    }

    onSubmit?.(data as any);
  };

  const handleCancel = () => {
    onCancel?.();
  };

  const incrementSplitProgram = () => {
    const currentValue = splitProgramValue || 1;
    const newCount = currentValue + 1;
    setValue("split_program", newCount);

    // Add new split to the splits array
    const currentSplits = splitsValue || [];
    const newSplits = [...currentSplits];
    while (newSplits.length < newCount) {
      newSplits.push({
        title: "",
        full_price: 0,
        subscription: 0,
        split_id: generateUUID(),
      });
    }
    setValue("splits", newSplits);
  };

  const decrementSplitProgram = () => {
    const currentValue = splitProgramValue || 1;
    if (currentValue > 1) {
      const newCount = currentValue - 1;
      setValue("split_program", newCount);

      // Remove splits from the array
      const currentSplits = splitsValue || [];
      const newSplits = currentSplits.slice(0, newCount);
      setValue("splits", newSplits);
    }
  };

  const incrementDaysForPreview = () => {
    const currentValue = daysForPreviewValue || 1;
    if (currentValue < 7) {
      setValue("days_for_preview", currentValue + 1);
    }
  };

  const decrementDaysForPreview = () => {
    const currentValue = daysForPreviewValue || 1;
    if (currentValue > 1) {
      setValue("days_for_preview", currentValue - 1);
    }
  };

  const handleFormError = (errors: any) => {
    console.log("Form errors:", errors);
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4 lg:p-6">
      {/* Header */}
      <div className="mb-6 flex justify-between">
        <h1 className="text-2xl font-bold text-text font-['Inter']">
          Create Program (1/2)
        </h1>

        <InteractiveButton
          type="button"
          onClick={() => {
            // add dummy data

            reset(programStepOneData);
          }}
          className="bg-transparent hidden text-primary border border-primary hover:bg-primary-hover hover:text-white text-sm px-3 py-1 rounded"
        >
          Add Data
        </InteractiveButton>
      </div>

      {/* Main Form Container */}
      <div className="bg-background rounded-md shadow-sm border border-border p-6">
        <form
          onSubmit={handleSubmit(handleFormSubmit, handleFormError)}
          className="space-y-8"
        >
          {/* Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              <ProgramBasicInfoSection
                register={register as any}
                errors={errors as any}
                programDescriptionValue={programDescriptionValue}
              />

              <PaymentPlansSection
                register={register as any}
                errors={errors as any}
              />

              <ProgramSettingsSection register={register as any} />
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              <TargetLevelsSection
                register={register as any}
                errors={errors as any}
              />

              <SplitConfigurationSection
                register={register as any}
                errors={errors as any}
                splitProgramValue={splitProgramValue}
                onIncrementSplitProgram={incrementSplitProgram}
                onDecrementSplitProgram={decrementSplitProgram}
              />

              <SplitDetailsSection
                register={register as any}
                errors={errors as any}
                splitsValue={splitsValue}
                paymentPlanValue={(paymentPlanValue || []).filter(
                  (item): item is string => item !== undefined
                )}
              />

              <PreviewSection
                register={register as any}
                daysForPreviewValue={daysForPreviewValue}
                onIncrementDaysForPreview={incrementDaysForPreview}
                onDecrementDaysForPreview={decrementDaysForPreview}
              />
            </div>
          </div>

          <FormActionButtons isValid={isValid} onCancel={handleCancel} />
        </form>
      </div>
    </div>
  );
};

export default CreateProgramStepOne;
export { type FormData as StepOneFormData };
