import { StepOneFormData } from "./CreateProgramStepOne";

export interface FormData {
  program_name: string;
  type_of_program: string;
  program_description: string;
  payment_plan: string[];
  track_progress: boolean;
  allow_comments: boolean;
  allow_private_messages: boolean;
  target_levels: string[];
  split_program: number;
  splits: Array<{
    title: string;
    full_price?: number;
    subscription?: number;
    split_id: string;
  }>;
  currency: string;
  days_for_preview: number;
}

export interface CreateProgramStepOneProps {
  onSubmit?: (data: FormData) => void;
  onCancel?: () => void;
  componentId?: number;
  initialData?: StepOneFormData | null;
}
