export const customApiPaths = {
  ebadollar: {
    // Member Profile Management
    member: {
      profile: {
        get: "/v2/api/ebadollar/custom/member/profile",
        update: "/v2/api/ebadollar/custom/member/profile",
        photo: {
          upload: "/v2/api/ebadollar/custom/member/profile/photo",
          delete: "/v2/api/ebadollar/custom/member/profile/photo",
        },
        changePassword:
          "/v2/api/ebadollar/custom/member/profile/change-password",
      },
      verification: {
        status: "/v2/api/ebadollar/custom/member/verification/status",
        requirements:
          "/v2/api/ebadollar/custom/member/verification/requirements",
        uploadDocument:
          "/v2/api/ebadollar/custom/member/verification/upload-document",
        submit: "/v2/api/ebadollar/custom/member/verification/submit",
      },
      deliveryAgent: {
        apply: "/v2/api/ebadollar/custom/member/delivery-agent/apply",
        status: "/v2/api/ebadollar/custom/member/delivery-agent/status",
        assignments:
          "/v2/api/ebadollar/custom/member/delivery-agent/assignments",
        updateAssignmentStatus:
          "/v2/api/ebadollar/custom/member/delivery-agent/assignments/:id/status",
      },
      auth: {
        changePassword: "/v2/api/ebadollar/custom/member/change-password",
      },
      paymentMethods: {
        list: "/v2/api/ebadollar/custom/member/payment-methods",
        add: "/v2/api/ebadollar/custom/member/payment-methods",
        setDefault:
          "/v2/api/ebadollar/custom/member/payment-methods/:id/set-default",
        remove: "/v2/api/ebadollar/custom/member/payment-methods/:id",
      },
      topUp: {
        requests: "/v2/api/ebadollar/custom/member/top-up-requests",
        cancel: "/v2/api/ebadollar/custom/member/top-up-requests/:id/cancel",
        history: "/v2/api/ebadollar/custom/member/top-up-history",
      },
      eba: {
        purchase: "/v2/api/ebadollar/custom/member/eba-purchase",
        balance: "/v2/api/ebadollar/custom/member/eba-balance",
        transactions: "/v2/api/ebadollar/custom/member/eba-transactions",
      },
      exchange: {
        listings: "/v2/api/ebadollar/custom/member/exchange-listings",
        buyRequests: "/v2/api/ebadollar/custom/member/buy-requests",
        sellRequests: "/v2/api/ebadollar/custom/member/sell-requests",
      },
      deliverySettings: {
        get: "/v2/api/ebadollar/custom/member/delivery-settings",
        save: "/v2/api/ebadollar/custom/member/delivery-settings/save",
        availability:
          "/v2/api/ebadollar/custom/member/delivery-settings/availability",
      },
      account: {
        delete: "/v2/api/ebadollar/custom/member/account/delete",
        export: "/v2/api/ebadollar/custom/member/account/export",
        data: "/v2/api/ebadollar/custom/member/account/data",
      },
    },
    // Admin endpoints
    deliveryApplications: {
      list: "/v2/api/ebadollar/custom/admin/delivery-applications",
      getById: "/v2/api/ebadollar/custom/admin/delivery-applications/:id",
      updateStatus:
        "/v2/api/ebadollar/custom/admin/delivery-applications/:id/status",
      getDocuments:
        "/v2/api/ebadollar/custom/admin/delivery-applications/:application_id/documents",
    },
    deliveryDocuments: {
      update: "/v2/api/ebadollar/custom/admin/delivery-documents/:document_id",
    },
    deliveryAgentComplaints: {
      list: "/v2/api/ebadollar/custom/admin/delivery-agent-complaints",
      getById: "/v2/api/ebadollar/custom/admin/delivery-agent-complaints/:id",
    },
  },
};
